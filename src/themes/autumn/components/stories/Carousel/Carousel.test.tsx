import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

import { createStore } from 'store/store';
import { TestWrapper, genMockStories } from 'util/jest';

import { ArrowLabel, CarouselMobileMode, ContainerType } from './enums';

import { Carousel } from '.';

import type { Story } from 'types/Story';

interface MockSliderProps {
  beforeChange?: (oldIndex: number, newIndex: number) => void;
  children: React.ReactNode;
  className?: string;
}

interface MockCarouselItemProps {
  onClick?: () => void;
  story: Story;
}

// Mock react-slick
jest.mock('react-slick', () => {
  const MockSlider = ({
    beforeChange,
    children,
    className
  }: MockSliderProps) => (
    <div className={className} data-testid="mock-slider">
      <button
        data-testid="mock-prev-arrow"
        onClick={() => beforeChange?.(1, 0)}
        type="button"
      >
        Previous
      </button>
      <button
        data-testid="mock-next-arrow"
        onClick={() => beforeChange?.(0, 1)}
        type="button"
      >
        Next
      </button>
      <div data-testid="slider-content">{children}</div>
    </div>
  );
  return MockSlider;
});

// Mock GTM tracking
const mockSendGtmInteractionEvent = jest.fn();
jest.mock('themes/autumn/templates/Classifieds/utils', () => ({
  sendGtmInteractionEvent: mockSendGtmInteractionEvent,
}));

// Mock CarouselItem component
jest.mock('./CarouselItem', () =>
  function MockCarouselItem({ onClick, story }: MockCarouselItemProps) {
    return (
      <div data-testid={`carousel-item-${story.id}`}>
        <h3>{story.title}</h3>
        <button
          data-testid={`story-click-${story.id}`}
          onClick={onClick}
          type="button"
        >
          Click Story
        </button>
      </div>
    );
  }
);

describe('Carousel Component', () => {
  const mockStories = genMockStories({ length: 3 });
  const store = createStore();

  const defaultProps = {
    stories: mockStories,
    title: 'Test Carousel',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Essential Test 1: Basic Rendering and Props', () => {
    it('renders with required props and displays stories', () => {
      const { container } = render(
        <TestWrapper store={store}>
          <Carousel
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      // Check that the carousel renders
      expect(screen.getByTestId('mock-slider')).toBeInTheDocument();

      // Check that all stories are rendered
      mockStories.forEach((story) => {
        const carouselItem = screen.getByTestId(`carousel-item-${story.id}`);
        expect(carouselItem).toBeInTheDocument();
        expect(screen.getByText(story.title)).toBeInTheDocument();
      });

      // Check that the title is displayed
      expect(screen.getByText('Test Carousel')).toBeInTheDocument();

      // Snapshot test for basic structure
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with optional props correctly', () => {
      render(
        <TestWrapper store={store}>
          <Carousel
            container={ContainerType.MAIN}
            mobileMode={CarouselMobileMode.CENTER_MODE}
            stories={defaultProps.stories}
            title={defaultProps.title}
            url="/test-url"
            useStrapHeading
          />
        </TestWrapper>,
      );

      // Check that the slider has the correct mobile mode class
      const slider = screen.getByTestId('mock-slider');
      expect(slider).toHaveClass('center-mode');
      expect(slider).not.toHaveClass('left-align-mode');
    });

    it('applies correct mobile mode classes', () => {
      const { rerender } = render(
        <TestWrapper store={store}>
          <Carousel
            mobileMode={CarouselMobileMode.LEFT_ALIGN_MODE}
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      let slider = screen.getByTestId('mock-slider');
      expect(slider).toHaveClass('left-align-mode');
      expect(slider).not.toHaveClass('center-mode');

      rerender(
        <TestWrapper store={store}>
          <Carousel
            mobileMode={CarouselMobileMode.CENTER_MODE}
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      slider = screen.getByTestId('mock-slider');
      expect(slider).toHaveClass('center-mode');
      expect(slider).not.toHaveClass('left-align-mode');
    });
  });

  describe('Essential Test 2: Click Interactions and Event Handling', () => {
    it('handles story click events when onClick prop is provided', () => {
      const mockOnClick = jest.fn();

      render(
        <TestWrapper store={store}>
          <Carousel
            onClick={mockOnClick}
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      // Click on the first story
      const firstStoryButton = screen.getByTestId(
        `story-click-${mockStories[0].id}`
      );
      fireEvent.click(firstStoryButton);

      expect(mockOnClick).toHaveBeenCalledWith(mockStories[0]);
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('does not break when onClick prop is not provided', () => {
      render(
        <TestWrapper store={store}>
          <Carousel
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      // Should render without onClick handlers
      const firstStoryButton = screen.getByTestId(
        `story-click-${mockStories[0].id}`
      );

      // This should not throw an error
      expect(() => fireEvent.click(firstStoryButton)).not.toThrow();
    });

    it('tracks arrow click events with GTM', () => {
      render(
        <TestWrapper store={store}>
          <Carousel
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      // Simulate arrow click by triggering beforeChange
      const nextButton = screen.getByTestId('mock-next-arrow');
      fireEvent.click(nextButton);

      expect(mockSendGtmInteractionEvent).toHaveBeenCalledWith(
        {
          action: 'arrow_click',
          category: 'articles',
          label: ArrowLabel.RIGHT,
          section: 'Test Carousel',
        },
        'carousel_interaction',
      );
    });
  });

  describe('Essential Test 3: Slider State Management', () => {
    it('manages slide state correctly during navigation', () => {
      render(
        <TestWrapper store={store}>
          <Carousel
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      // Test forward navigation
      const nextButton = screen.getByTestId('mock-next-arrow');
      fireEvent.click(nextButton);

      expect(mockSendGtmInteractionEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          label: ArrowLabel.RIGHT,
        }),
        'carousel_interaction',
      );

      // Test backward navigation
      const prevButton = screen.getByTestId('mock-prev-arrow');
      fireEvent.click(prevButton);

      expect(mockSendGtmInteractionEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          label: ArrowLabel.LEFT,
        }),
        'carousel_interaction',
      );
    });

    it('handles empty stories array gracefully', () => {
      render(
        <TestWrapper store={store}>
          <Carousel stories={[]} title={defaultProps.title} />
        </TestWrapper>,
      );

      // Should still render the carousel structure
      expect(screen.getByTestId('mock-slider')).toBeInTheDocument();
      expect(screen.getByText('Test Carousel')).toBeInTheDocument();

      // But no story items should be present
      expect(screen.queryByTestId(/carousel-item-/)).not.toBeInTheDocument();
    });

    it('passes correct settings to slider based on container type', () => {
      render(
        <TestWrapper store={store}>
          <Carousel
            container={ContainerType.MAIN}
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      const slider = screen.getByTestId('mock-slider');
      expect(slider).toBeInTheDocument();

      // The slider should receive the settings from sliderSettingsMap
      // This tests that the component correctly maps container types
    });
  });
});
