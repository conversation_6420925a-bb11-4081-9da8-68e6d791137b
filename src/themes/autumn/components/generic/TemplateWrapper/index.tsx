'use client';

import clsx from 'clsx';
import React, { useEffect, useMemo, useState } from 'react';

import HelpButton from 'components/HelpButton';
import PianoHook from 'components/Piano/PianoHook';
import Breadcrumb from 'components/schema/Breadcrumb';
import HelpCentreSchema from 'components/schema/HelpCentre';
import LiveBlogSchema from 'components/schema/LiveBlog';
import NewsArticle from 'components/schema/NewsArticle';
import Organization from 'components/schema/Organization';
import SocialProfile from 'components/schema/SocialProfile';
import UgcEvent from 'components/schema/UgcEvent';
import VideoArticle from 'components/schema/Video';
import { isVideoStory } from 'components/schema/Video/utils';
import { useAppSelector } from 'store/hooks';
import RevBanner from 'themes/autumn/components/RevBanner';
import Outage from 'themes/autumn/components/outage/Outage';
import CommentCountProvider from 'themes/autumn/components/stories/Comments/CommentCountContext';
import ViafouraScript from 'themes/autumn/components/stories/Comments/ViafouraScript';
import { Service, ServiceType, StoryElementType } from 'types/Story';
import { StoryViewType } from 'types/ZoneItems';
import { UgcContentType } from 'types/ugc';
import { useAuthToasts } from 'util/auth';

import AgsBanner from '../../AgsBanner';
import BottomAnchorAd from '../../ads/BottomAnchorAd';
import GutterAd from '../../ads/GutterAd';
import StickyFooterAd from '../../ads/StickyFooterAd';
import CreatePasswordModal from '../../auth/CreatePasswordModal';
import Footer from '../../footer/Footer';
import MostViewedNavBar from '../../nav/MostViewedNavBar';
import Nav from '../../nav/Nav';
import NavContext from '../../nav/NavContext';
import NewsletterProvider from '../../newsletter/NewsletterProvider';
import SmartBanner from '../../smartbanner/SmartBanner';

import styles from './styles.module.css';

import type { NavigationForcedStyle } from '../../nav/NavContext';
import type { NavigationAdPosition } from '../../nav/NavContext/enums';
import type { GenericElement } from 'types/Story';

interface TemplateWrapperProps {
  bgColor?: string;
  bgImage?: string;
  children?: React.ReactNode;
  className?: string;
  footer?: React.ReactElement | null;
  free?: boolean;
  hideSmartBanner?: boolean;
  nav?: React.ReactElement | null;
  navigationAdPosition?: NavigationAdPosition;
  navigationHideShortcuts?: boolean;
  navigationStickyMobileContent?: React.ReactElement;
  navigationStickyWideContent?: React.ReactElement;
  navigationStyle?: NavigationForcedStyle;
  seoDescription?: string;
  seoTitle?: string;
  showGutterAd?: boolean;
  showHelp?: boolean;
  showMeter?: boolean;
  showMostViewedNavBar?: boolean;
  showNavigationAd?: boolean;
  showOutages?: boolean;
  showReCaptchaBadge?: boolean;
  showRevBanner?: boolean;
  showStickyFooterAd?: boolean;
}

export default function TemplateWrapper({
  bgColor = 'bg-white',
  bgImage,
  children,
  className,
  footer,
  free = false,
  hideSmartBanner = false,
  nav,
  navigationAdPosition,
  navigationHideShortcuts,
  navigationStickyMobileContent,
  navigationStickyWideContent,
  navigationStyle,
  seoDescription = '',
  seoTitle = '',
  showGutterAd = false,
  showHelp = false,
  showMeter = false,
  showMostViewedNavBar = false,
  showNavigationAd = true,
  showOutages = true,
  showReCaptchaBadge = false,
  showRevBanner = false,
  showStickyFooterAd = false,
}: TemplateWrapperProps): React.ReactElement {
  const hideRevBanner = useAppSelector((state) => state.page.hideRevBanner);
  const view = useAppSelector((state) => state.settings.viewType);
  const story = useAppSelector((state) => state.story);
  const ugc = useAppSelector((state) => state.ugc.ugcDetail);

  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const hasBottomAnchorAd = useAppSelector(
    (state) => state.adServing.hasBottomAnchorAd,
  );
  const hasPaywall = useAppSelector((state) => state.piano.hasPaywall);
  const { piano: pianoFeature, recaptchaV3: reCaptchaFeature } =
    useAppSelector((state) => state.features);

  const [hasSetTag, setHasSetTag] = useState(false);
  useEffect(() => {
    if (!pianoFeature.enabled) {
      return;
    }

    if (!hasSetTag && free) {
      window.tp = window.tp || [];
      window.tp.push(['setTags', ['free', 'ct-free']]);
      setHasSetTag(true);
    }
  }, [pianoFeature.enabled, free, hasSetTag, setHasSetTag]);

  const hideReCaptchaBadge =
    reCaptchaFeature.enabled && !showReCaptchaBadge && !hasPaywall;

  const [isVideo] = isVideoStory(view, story);
  const showRev =
    showRevBanner && !hideRevBanner && !story.tags?.includes('property');

  const dailymotionPipEnabledMobile = useAppSelector(
    (state) => state.conf.dailymotionPipEnabledMobile,
  );

  useAuthToasts();

  const navContextValue = useMemo(
    () => ({
      forcedAdPosition: navigationAdPosition,
      forcedStyle: navigationStyle,
      hideShortcuts: navigationHideShortcuts ?? false,
      showNavigationAd: showNavigationAd ?? false,
      stickyMobileContent: navigationStickyMobileContent,
      stickyWideContent: navigationStickyWideContent,
    }),
    [
      navigationHideShortcuts,
      navigationStickyMobileContent,
      navigationStickyWideContent,
      showNavigationAd,
      navigationAdPosition,
      navigationStyle,
    ],
  );

  useEffect(() => {
    if (hideReCaptchaBadge) {
      document.body.classList.add(styles.hideReCaptchaBadge);
      return () => {
        document.body.classList.remove(styles.hideReCaptchaBadge);
      };
    }

    return undefined;
  }, [hideReCaptchaBadge]);

  // check if there's a norkon live blog element in the story
  const norkonFeature = useAppSelector(
    (state) => state.features.norkonLiveblog,
  );

  const liveBlogElement = norkonFeature.enabled
    ? story.elements?.find(
        (element): element is GenericElement =>
          element.type === StoryElementType.Generic &&
          element.service === Service.Norkon &&
          element.serviceType === ServiceType.Blog,
      )
    : undefined;

  const schemaComponent = useMemo<React.ReactElement | null>(() => {
    if (liveBlogElement) {
      return (
        <LiveBlogSchema
          description={liveBlogElement.description}
          serviceId={liveBlogElement.serviceId}
        />
      );
    }

    if (isVideo) {
      return <VideoArticle />;
    }
    if (ugc?.id) {
      return ugc.contentType === UgcContentType.EVENT ? (
        <UgcEvent seoDescription={seoDescription} seoTitle={seoTitle} />
      ) : null;
    }
    if ((view as StoryViewType) === StoryViewType.HELP_CENTRE_STORY) {
      return <HelpCentreSchema seoTitle={seoTitle} />;
    }
    return <NewsArticle seoDescription={seoDescription} seoTitle={seoTitle} />;
  }, [liveBlogElement, isVideo, seoDescription, seoTitle, ugc, view]);

  return (
    <NavContext.Provider value={navContextValue}>
      {schemaComponent}
      <Breadcrumb title={seoTitle} />
      {(view as StoryViewType) === StoryViewType.HELP_CENTRE_STORY ? (
        <Organization />
      ) : (
        <SocialProfile />
      )}
      {pianoFeature.enabled && showMeter && (
        <PianoHook id="top-meter-banner" />
      )}
      {!hideSmartBanner && !dailymotionPipEnabledMobile && <SmartBanner />}
      {showOutages && <Outage />}
      {nav !== null && (nav ?? <Nav />)}
      {showRev && (
        <div className="mx-auto w-full max-w-sm md:max-w-lg">
          <RevBanner />
        </div>
      )}
      <AgsBanner wrapperClass="mx-auto w-full max-w-sm md:max-w-lg" />
      <div
        className={clsx(bgColor, className, styles.bonzaiWrapper)}
        style={{
          backgroundImage: bgImage
            ? `url("${encodeURI(bgImage)}")`
            : undefined,
        }}
      >
        {showGutterAd && <GutterAd />}
        <NewsletterProvider>
          <CommentCountProvider>{children}</CommentCountProvider>
        </NewsletterProvider>
        {/* footer has three modes
          - null: display no footer
          - undefined: default footer -> <Footer/>
          - <component>: display footer component */}
        {footer !== null && (footer ?? <Footer />)}
      </div>
      <div className="sticky bottom-0 z-30 flex flex-col">
        {showStickyFooterAd && <StickyFooterAd />}
        {pianoFeature.enabled && showMeter && (
          <>
            <div
              className="pointer-events-none fixed inset-0 z-10 hidden h-full opacity-0"
              id="piano-overlay"
            />

            <PianoHook
              className={clsx('inset-x-0 z-40 bg-transparent', styles.meter)}
              id="metered-banner"
            />
          </>
        )}
      </div>
      {pianoFeature.enabled && (
        <div
          className={clsx(
            'fixed bottom-20 left-1/2 right-auto z-50 mx-auto flex max-h-56 w-full max-w-[310px] -translate-x-2/4 items-center justify-center overflow-hidden rounded-lg md:max-w-[510px]',
            {
              'md:bottom-4': !hasBottomAnchorAd,
              'md:bottom-24': hasBottomAnchorAd,
            },
          )}
          id="popup-container"
        />
      )}
      <HelpButton show={showHelp} />
      <ViafouraScript />
      <BottomAnchorAd />
      <CreatePasswordModal />
      {showMostViewedNavBar && (
        <>
          <MostViewedNavBar />
          <div
            className={clsx('sticky bottom-0 z-50 h-min', {
              'mx-auto max-w-[1220px]': hasTakeoverAd,
            })}
            id="mostViewedNavBarDesktop"
          />
        </>
      )}
    </NavContext.Provider>
  );
}
