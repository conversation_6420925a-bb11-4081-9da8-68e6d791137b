import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';

import Container from '../../../components/generic/Container';
import FaqContainerACM from '../common/FaqContainerACM';
import FlowCheckout from '../common/FlowCheckout';
import OptionCard, { Checkmark } from '../common/FlowCheckout/OptionCard';
import OtherOffers from '../common/FlowCheckout/OtherOffers';
import {
  Period,
  Plan,
  calculateTermIndexACM,
  truncateWholeNumbers,
  useTermHelpers,
} from '../common/FlowCheckout/util';

import type { PlanMeta } from '../common/FlowCheckout/util';

const planMeta: PlanMeta = {
  [Plan.BASIC]: {
    description: (
      <>
        <Checkmark bold>All articles from the website</Checkmark>
        <Checkmark bold>Subscriber exclusive app</Checkmark>
        <Checkmark bold>
          All articles from the other regional websites in your area
        </Checkmark>
      </>
    ),
    name: 'Basic Digital',
    tag: 'Basic Digital',
  },
  [Plan.PREMIUM]: {
    description: (
      <>
        <Checkmark bold>Everything in basic digital</Checkmark>
        <Checkmark bold>Digital version of Today&apos;s Paper</Checkmark>
        <Checkmark bold>Premium puzzles access</Checkmark>
      </>
    ),
    name: 'Unlimited Digital',
    primary: {
      border: false,
      button: true,
    },
    tag: 'Premium Digital',
  },
};

function SubscribePageSpringPromoFlow(): React.ReactElement | null {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const terms = useAppSelector((state) => state.piano.terms);
  const staticSiteUrl = useAppSelector(
    (state) => state.settings.staticSiteUrl,
  );

  const termHelpers = useTermHelpers(
    calculateTermIndexACM,
    terms,
    [Plan.BASIC, Plan.PREMIUM],
    [Period.FOUR_WEEKLY, Period.YEARLY],
  );
  const {
    calculateTermIndex,
    getOriginalPricing,
    getPrice,
    getTrialPeriod,
    getWeeklyPrice,
  } = termHelpers;

  const subscribeImgPath = `${staticSiteUrl}images/subscribe/springpromo`;
  const mobileImageUrl = `${subscribeImgPath}/mobile.png`;
  const desktopImageUrl = `${subscribeImgPath}/desktop.jpg`;

  if (!pianoFeature.enabled) {
    return null;
  }

  const basicMonthlyTnC = {
    originalPrice: truncateWholeNumbers(
      getOriginalPricing(Plan.BASIC, Period.FOUR_WEEKLY, false, false),
    ),
    price: truncateWholeNumbers(
      getPrice(Plan.BASIC, Period.FOUR_WEEKLY, false, false),
    ),
    trialPeriod: getTrialPeriod(Plan.BASIC, Period.FOUR_WEEKLY, false),
    weeklyPrice: truncateWholeNumbers(
      getWeeklyPrice(Plan.BASIC, true, Period.FOUR_WEEKLY),
    ),
  };
  const premiumMonthlyTnC = {
    originalPrice: truncateWholeNumbers(
      getOriginalPricing(Plan.PREMIUM, Period.FOUR_WEEKLY, false, false),
    ),
    price: truncateWholeNumbers(
      getPrice(Plan.PREMIUM, Period.FOUR_WEEKLY, false, false),
    ),
    trialPeriod: getTrialPeriod(Plan.PREMIUM, Period.FOUR_WEEKLY, false),
    weeklyPrice: truncateWholeNumbers(
      getWeeklyPrice(Plan.PREMIUM, true, Period.FOUR_WEEKLY),
    ),
  };

  const termsConditions = (
    <div className="mx-auto my-0 w-4/5 border-b border-solid border-gray-200 px-0 pb-11 pt-2 text-center font-inter text-xs text-gray-500 md:border-0 md:border-none md:border-transparent md:pb-0">
      <sup>*</sup>
      Monthly Basic Digital Subscription offer costs{' '}
      {basicMonthlyTnC.weeklyPrice} per week {basicMonthlyTnC.trialPeriod},
      billed every 4 weeks (min cost {basicMonthlyTnC.price}), then{' '}
      {basicMonthlyTnC.originalPrice} billed monthly. Monthly Premium Digital
      Subscription offer costs {premiumMonthlyTnC.weeklyPrice} per week{' '}
      {premiumMonthlyTnC.trialPeriod}, billed every 4 weeks (min cost{' '}
      {premiumMonthlyTnC.price}), then {premiumMonthlyTnC.originalPrice} billed
      monthly. Renewals occur unless cancelled. Not in conjunction with any
      other offer. New customers only. Offer ends October 5, 2025. See{' '}
      <Link
        className="text-blue-600"
        href="/about-us/terms-conditions/digital-subscription/"
        noStyle
        target="_blank"
      >
        Terms & Conditions
      </Link>{' '}
      for full subscription terms.
    </div>
  );

  return (
    <FlowCheckout
      defaultPeriod={Period.FOUR_WEEKLY}
      planMeta={planMeta}
      termHelpers={termHelpers}
    >
      <div>
        <div className="relative flex w-full flex-col items-center">
          <picture>
            <source media="(max-width: 767px)" srcSet={mobileImageUrl} />
            <img
              alt="Spring Promo"
              className="h-auto w-full"
              src={desktopImageUrl}
            />
          </picture>
        </div>

        <div className="mx-auto">
          <div
            className={clsx(
              'transition-opacity duration-200',
              terms.length === 4 ? 'opacity-100' : 'opacity-0',
            )}
          >
            <div className="flex flex-col items-center justify-start gap-y-10 pt-7 lg:pt-10">
              <div className="flex flex-col justify-center gap-y-6 lg:flex-row lg:gap-x-6 lg:gap-y-0">
                {[Plan.BASIC, Plan.PREMIUM].map((plan) => {
                  const periodToShow = Period.FOUR_WEEKLY;
                  const simplePlan =
                    terms[calculateTermIndex(plan, periodToShow, false)];
                  const trialPeriod = simplePlan?.firstPlan?.format(
                    ({ duration }) => `For the first ${duration}`,
                  );
                  const termsSubtext = simplePlan?.firstPlan?.format(
                    ({ period, price }) => (
                      <div className="-mb-2">
                        Billed ${price} every {period} (min cost)
                      </div>
                    ),
                    {
                      truncateUnnecessaryDecimals: true,
                    },
                  );
                  const trialPeriodSubText = 'per week';
                  // eslint-disable-next-line @stylistic/max-len
                  const billedSubtext = `Then billed ${truncateWholeNumbers(getOriginalPricing(plan, periodToShow, false))} monthly`;

                  return (
                    <OptionCard
                      billedSubtext={billedSubtext}
                      key={plan}
                      name={planMeta[plan]?.name ?? 'NULL'}
                      plan={plan}
                      price={getWeeklyPrice(plan, true, periodToShow)}
                      priceBeforeDiscount={getWeeklyPrice(
                        plan,
                        false,
                        periodToShow,
                      )}
                      primary={planMeta[plan]?.primary}
                      tag={planMeta[plan]?.tag}
                      termsSubtext={termsSubtext}
                      trialPeriod={trialPeriod}
                      trialPeriodSubText={trialPeriodSubText}
                    >
                      <div className="flex flex-col gap-y-5 pt-[27px]">
                        {planMeta[plan]?.description ?? 'NULL'}
                      </div>
                    </OptionCard>
                  );
                })}
              </div>
              {termsConditions}
            </div>
          </div>
          <OtherOffers />

          <div className="mx-auto max-w-[960px] lg:pb-8">
            <Container>
              <FaqContainerACM />
            </Container>
          </div>
        </div>
      </div>
    </FlowCheckout>
  );
}

export default SubscribePageSpringPromoFlow;
