import clsx from 'clsx';
import Script from 'next/script';
import React, { useEffect, useState } from 'react';

import Skimlinks from 'components/Skimlinks';
import { useAppSelector } from 'store/hooks';
import { PageThemeVariant, ThemeVariant } from 'store/slices/conf';
import { PianoABTestingVariant } from 'store/slices/piano';
import Ad from 'themes/autumn/components/ads/Ad';
import OwnLocal from 'themes/autumn/components/ads/OwnLocal';
import Taboola from 'themes/autumn/components/ads/Taboola';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import NewsletterProvider from 'themes/autumn/components/newsletter/NewsletterProvider';
import NewsletterSlider from 'themes/autumn/components/newsletter/NewsletterSlider';
import MopStoryRecirculationSection from 'themes/autumn/components/recirculation/MopStoryRecirculationSection';
import RecirculationSections from 'themes/autumn/components/recirculation/RecirculationSections';
import { Carousel } from 'themes/autumn/components/stories/Carousel';
import { ContainerType } from 'themes/autumn/components/stories/Carousel/enums';
import Comments from 'themes/autumn/components/stories/Comments';
import Signpost from 'themes/autumn/components/stories/Signpost';
import StoryAuthorsBio from 'themes/autumn/components/stories/StoryAuthorsBio';
import StoryBreadcrumb from 'themes/autumn/components/stories/StoryBreadcrumb';
import StoryElements from 'themes/autumn/components/stories/StoryElements';
import StoryPageHeadline from 'themes/autumn/components/stories/StoryPageHeadline';
import LocalPartner from 'themes/autumn/components/stories/StoryPageHeadline/LocalPartner';
import { TitledStoryListTwoColumns } from 'themes/autumn/components/stories/TitledStoryList';
import ExclusiveHomeWidget from 'themes/autumn/templates/ExclusiveHomeWidget';
import CommentCount from 'themes/autumn/templates/stories/common/CommentCount';
import DraftStoryBanner from 'themes/autumn/templates/stories/common/DraftStoryBanner';
import SaveButton from 'themes/autumn/templates/stories/common/SaveButton';
import ShareButton from 'themes/autumn/templates/stories/common/ShareButton';
import LocalAdUnitBase from 'themes/autumn/templates/zoneItems/classified/LocalAdUnit/LocalAdUnitBase';
import LocalAdUnitSidebar from 'themes/autumn/templates/zoneItems/classified/LocalAdUnit/LocalAdUnitSidebar';
import OwnLocalAds from 'themes/autumn/templates/zoneItems/classified/OwnLocalAds';
import MailingList from 'themes/autumn/templates/zoneItems/mailinglist/Default/MailingList';
import RevWidget from 'themes/autumn/templates/zoneItems/revwidget/Default';
import UpcomingMatches from 'themes/autumn/templates/zoneItems/sportsHub/UpcomingMatches/components/UpcomingMatches';
import { SportPage } from 'types/SportsHub';
import { RecirculationContentType } from 'types/Story';
import { WidgetPosition } from 'types/businessProfiles';
import useAbTest, {
  AbExperiment,
  AbTest,
  useAbExperiment,
} from 'util/ab-tests';
import { AdSize } from 'util/ads';
import useMobileDetect, { DeviceType } from 'util/device';
import { sendAbTestImpression, setGtmDataLayer } from 'util/gtm';
import {
  useDeviceTypeFromWidth,
  useFetchStyleByTheme,
  useLazyLoadComponentState,
  useOnce,
  usePageTheme,
  usePianoABTestingState,
  useWindowHref,
} from 'util/hooks';
import InViewEvent from 'util/inViewEvent';
import {
  fetchStoriesForStoryList,
  fetchStoryListsByTitle,
} from 'util/organization/suzuka';

import FarmBuyScript from '../../zoneItems/advertisement/FarmBuyWidget/FarmBuyScript';

import type {
  RecirculationSectionStoryList,
  Story as StoryType,
} from 'types/Story';

const stickySpacer = <div className="hidden h-60 lg:block" />;
const STUD_SALE_SUFFIX = 'Stud Sale';

const onView = (name: string) => {
  setGtmDataLayer({
    data: {
      action: 'impression',
      label: null,
      section: name,
    },
    event: 'recirculation_impression',
  });
};

interface StoryProps {
  showLocalPartner?: boolean;
}

function Story({ showLocalPartner = false }: StoryProps): React.ReactElement {
  const story = useAppSelector((state) => state.story);
  const {
    name: pageName,
    storyList: pageStoryList,
    template: pageTemplate,
  } = useAppSelector((state) => state.page);
  const siteId = useAppSelector((state) => state.settings.siteId);
  const siteName = useAppSelector((state) => state.conf.name);
  const studSaleStorylistTitle = `${siteName} ${STUD_SALE_SUFFIX}`;

  // state for Featured Stud Sale carousel
  const [featuredStudSaleStories, setFeaturedStudSaleStories] = useState<
    StoryType[]
  >([]);
  const oovvuuFeature = useAppSelector((state) => state.features.oovvuu);
  const ownLocal = useAppSelector((state) => state.features.ownLocal);
  const businessProfilesFeature = useAppSelector(
    (state) => state.features.businessProfiles,
  );
  const hasDirectory = useAppSelector(
    (state) =>
      state.features.ownLocal.enabled &&
      state.features.ownLocal.data.hasDirectory,
  );
  const themeVariant = useAppSelector((state) => state.conf.themeVariant);

  const { host, isSecure, transformUrl } = useAppSelector(
    (state) => state.settings,
  );
  const supportNewslettersLandingPage = useAppSelector(
    (state) =>
      state.features.mail.enabled &&
      state.features.mail.data.supportNewslettersLandingPage,
  );
  const newsletterWidgetHeading =
    useAppSelector(
      (state) =>
        state.features.mail.enabled &&
        state.features.mail.data.supportNewslettersLandingPage &&
        state.features.mail.data.articleWidgetHeading,
    ) || '';
  const newsletterWidgetUrl =
    useAppSelector(
      (state) =>
        state.features.mail.enabled &&
        state.features.mail.data.supportNewslettersLandingPage &&
        state.features.mail.data.articleWidgetUrl,
    ) || '';
  const isRecommendationContentEnabled = useAppSelector(
    (state) =>
      state.features.piano.enabled &&
      state.features.piano.data.isRecommendationContentEnabled,
  );
  const { enablePianoABTesting, pianoABTestingVariant } =
    usePianoABTestingState();
  const communityRecirculationEnabled = useAppSelector(
    (state) => state.features.communityRecirculation.enabled,
  );
  // HACK: Ideally not hardcode this here.
  const isLocalContent =
    pageTemplate.startsWith('community/') ||
    pageName === 'Local News' ||
    pageName === 'Local Sport';
  const { recirculationSections } = story;
  const storyFontStyle = useFetchStyleByTheme('story', 'fontStyle');
  const seoTitle = story.seoTitle || story.title;
  const { seoDescription } = story;
  const absoluteUrl = `${isSecure ? 'https' : 'http'}://${host}${story.url}`;
  const commentsConfig = {
    id: story.id,
    state: story.comments,
    title: story.title,
    url: absoluteUrl,
  };

  const { showComponent, showComponentPlaceholder } =
    useLazyLoadComponentState();
  const isPropertyArticle = story.tags.includes('property');
  const isAgsTheme = themeVariant === ThemeVariant.AGS;
  const showFarmBuy = isAgsTheme && isPropertyArticle;
  const showNavAd = themeVariant === ThemeVariant.AGS || !isPropertyArticle;
  const pageTheme = usePageTheme();
  const showUpcomingMatchesWidget =
    pageTheme === PageThemeVariant.SPORT &&
    [SportPage.NRL, SportPage.AFL, SportPage.CRICKET].includes(
      pageName.toLowerCase() as SportPage,
    );
  const deviceType = useDeviceTypeFromWidth();
  const isMobile = deviceType === DeviceType.MOBILE;
  const limit = isMobile ? 4 : 6;
  const currentDevice = useMobileDetect();
  const isAbTestActive = useAbExperiment(AbExperiment.StoryPrimaryNav);
  const useNewBehaviour =
    useAbTest(AbTest.StoryPrimaryNav_A) && currentDevice.isMobile();
  const url = useWindowHref();

  useOnce(() => {
    if (isAbTestActive) {
      sendAbTestImpression();
      return true;
    }
    return false;
  }, [isAbTestActive]);

  // fetch stud sale storylist
  useEffect(() => {
    // eslint-disable-next-line compat/compat
    const controller = new AbortController();
    const { signal } = controller;

    fetchStoryListsByTitle(studSaleStorylistTitle, siteId, signal)
      .then((storylists) => {
        if (storylists.length > 0) {
          // ideally, each site has only one storylist with this title
          const firstStorylist = storylists[0];
          return fetchStoriesForStoryList({
            signal,
            siteId,
            storyListId: firstStorylist.id,
            useCanonicalUrl: false,
          });
        }
        return [];
      })
      .then((stories) => {
        setFeaturedStudSaleStories(stories);
      })
      .catch((error) => {
        const isCancellableError =
          error instanceof Error || error instanceof DOMException;

        if (isCancellableError && error.name === 'AbortError') {
          return;
        }

        console.error('Failed to fetch featured stud sale stories', error);
        setFeaturedStudSaleStories([]);
      });

    return () => {
      controller.abort();
    };
  }, [siteId, studSaleStorylistTitle]);

  const sourceComponent = showLocalPartner ? (
    <LocalPartner
      businessName={story.byline}
      publishFrom={story.publishFrom}
      tags={story.tags}
    />
  ) : (
    <StoryAuthorsBio
      authors={story.authors}
      className="md:mb-7"
      transformUrl={transformUrl}
    />
  );

  return (
    <TemplateWrapper
      seoDescription={seoDescription}
      seoTitle={seoTitle}
      showMeter
      showMostViewedNavBar
      showNavigationAd={showNavAd}
      showRevBanner
      showStickyFooterAd
    >
      <Container className="md:px-6 xl:px-0" noGutter>
        <DraftStoryBanner show={!story.publishable} />
        {isPropertyArticle && <RevWidget className="mt-4 md:mt-10" />}
        {oovvuuFeature.enabled && showComponent && (
          <>
            <div
              data-vertical={pageName === 'Sport' ? 'Sport' : 'News'}
              id="oovvuu-strap"
            />
            <Script async src="https://strap.oovvuu.com/RLPS.strap.js" />
          </>
        )}
        <div className="mb-8 mt-4 space-y-4 md:mt-7 md:space-y-7 lg:mt-14">
          {!useNewBehaviour && (
            <StoryBreadcrumb className="mx-auto px-4 md:px-0 lg:hidden" />
          )}
          <div className="mx-auto flex flex-row px-4 empty:hidden md:px-0">
            <Signpost
              className="mb-0 md:-mb-1"
              enablePagePalette={pageTheme !== PageThemeVariant.SPORT}
              max={2}
              publishFrom={story.publishFrom}
              tags={story.tags}
            />
          </div>
          <StoryPageHeadline
            authors={story.authors}
            byline={story.byline}
            commentsConfig={commentsConfig}
            publishFrom={story.publishFrom}
            showFarmBuy={showFarmBuy}
            showLocalPartner={showLocalPartner}
            tags={story.tags}
            title={seoDescription ? story.title : seoTitle}
            updatedOn={story.updatedOn}
            url={story.url}
          />
        </div>
        <div className="flex-col md:flex-row lg:flex">
          <div
            className={clsx(
              'w-full md:relative lg:w-7/10 lg:pr-9 xl:pr-10',
              storyFontStyle,
            )}
            // Required for Hindsight to insert their ad code
            // Might be removable once all sites are on Autumn
            id="story-body"
          >
            {oovvuuFeature.enabled && <div id="head-video" />}
            <StoryElements
              elements={story.elements}
              hideRecommendation={
                communityRecirculationEnabled && isLocalContent
              }
              showAds
              usePaywall
            />
            <div
              className="relative mx-auto my-14 px-4 md:my-20 md:px-0"
              id="story-footer"
            >
              <div className="invisible relative -top-12 block md:-top-16" />
              <div
                className={clsx(
                  'flex items-center justify-start space-x-1 divide-x divide-gray-200 border-y border-gray-200 py-2 font-inter md:py-4',
                  {
                    'mb-6': !story.authors.length,
                  },
                )}
              >
                <SaveButton className="ml-1" />
                <ShareButton
                  className="ml-1 rounded-none md:rounded-3xl"
                  url={url}
                />
                <CommentCount className="ml-1" story={story} />
              </div>
              {sourceComponent}
              {!!recirculationSections?.length &&
                enablePianoABTesting &&
                pianoABTestingVariant ===
                  PianoABTestingVariant.RECIRCULATION_B && (
                  <div className="mt-14 flex-col px-4 md:flex-col md:px-0 lg:flex">
                    {recirculationSections
                      .filter(
                        (r): r is RecirculationSectionStoryList =>
                          r.contentType === RecirculationContentType.StoryList,
                      )
                      .map((recirculationSection) => (
                        <MopStoryRecirculationSection
                          key={recirculationSection.url}
                          name={recirculationSection.name}
                          showCanonicalSite={
                            recirculationSection.showCanonicalSite
                          }
                          sliceCount={3}
                          stories={recirculationSection.stories}
                          url={recirculationSection.url}
                        />
                      ))}
                  </div>
                )}
            </div>
            <Comments
              className="mb-14 px-4 md:mb-20 md:px-0"
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...commentsConfig}
            />
            {featuredStudSaleStories.length >= 4 && (
              <div className="mb-14 px-4 md:mb-20 md:px-0">
                <Carousel
                  container={ContainerType.MAIN}
                  stories={featuredStudSaleStories}
                  title="Stud Reports"
                  useStrapHeading
                />
              </div>
            )}
            {isRecommendationContentEnabled && (
              <>
                <div id="recommendationBottomStories" />
                <div id="recommendationBottom">
                  <div className="mb-10 flex flex-col divide-y-1 border-b border-gray-300 pb-4 sm:flex-row md:grid-cols-2 md:divide-x-1 md:divide-y-0 md:divide-gray-300">
                    <div className="w-full sm:w-1/2">
                      <div className="p-2">
                        <div
                          className="mb-4 break-words border-t-2 border-gray-900 pt-2 font-inter text-base font-semibold text-gray-900 empty:hidden"
                          id="recommendationBottomLeftHeading"
                        />
                        <div
                          className="empty:hidden"
                          id="recommendationBottomLeft"
                        />
                      </div>
                    </div>
                    <div className="w-full sm:w-1/2">
                      <div className="mt-6 p-2 md:mt-0">
                        <div
                          className="mb-4 break-words border-t-2 border-gray-900 pt-2 font-inter text-base font-semibold text-gray-900 empty:hidden"
                          id="recommendationBottomRightHeading"
                        />
                        <div
                          className="empty:hidden"
                          id="recommendationBottomRight"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}

            <div className="mx-4 md:mx-0">
              <RecirculationSections sections={recirculationSections} />
            </div>

            {pageStoryList &&
              !enablePianoABTesting &&
              (!communityRecirculationEnabled || !isLocalContent) && (
                <InViewEvent
                  onEnterEvent={() => onView(`More from ${pageName}`)}
                >
                  <div className="mb-14 md:mb-20">
                    <div>
                      <TitledStoryListTwoColumns
                        className="my-6 px-4 md:px-0"
                        headlineProps={{
                          onClick: (clickedStory: typeof story) => {
                            setGtmDataLayer({
                              data: {
                                action: 'click',
                                label: clickedStory.title,
                                section: `More from ${pageName}`,
                              },
                              event: 'recirculation_click',
                            });
                          },
                          showByline: false,
                          showSignpost: false,
                        }}
                        isHeading
                        limit={limit}
                        stories={pageStoryList.stories}
                        storyListId={pageStoryList.storyListId}
                        title={`More from ${pageName}`}
                      />
                    </div>
                  </div>
                </InViewEvent>
              )}
            {oovvuuFeature.enabled && <div id="foot-video" />}
            {businessProfilesFeature.enabled &&
              businessProfilesFeature.data.widgetPosition ===
                WidgetPosition.Bottom &&
              (hasDirectory ? (
                <OwnLocalAds limit={3} />
              ) : (
                <div className="my-4 px-4 empty:hidden md:p-0 lg:my-0">
                  <LocalAdUnitBase
                    carouselBreakpoint="block"
                    showHeading
                    verticalStackBreakpoint="hidden"
                  />
                </div>
              ))}

            {supportNewslettersLandingPage && (
              <div className="mx-auto mb-6 px-4 md:px-0">
                <NewsletterProvider>
                  <NewsletterSlider
                    heading={newsletterWidgetHeading}
                    isSide={false}
                    url={newsletterWidgetUrl}
                  />
                </NewsletterProvider>
              </div>
            )}
            <ExclusiveHomeWidget />
            {story.publishable && showComponentPlaceholder && (
              <div className="mb-14 px-4 md:mb-20 md:px-0">
                <Taboola />
              </div>
            )}
          </div>
          <div className="w-full min-w-mrec space-y-14 px-4 md:space-y-0 md:p-0 lg:flex lg:w-3/10 lg:flex-col lg:space-y-10">
            {showComponentPlaceholder && (
              <div className="hidden empty:hidden lg:block">
                <div className="sticky top-20">
                  <Ad
                    className="mx-auto min-w-mrec lg:max-w-mrec xl:mx-2 xl:max-w-none"
                    labelSpace="space-y-2"
                    lgSizes={{
                      defaultPlaceholderSize: AdSize.mrec,
                      sizes: [AdSize.halfPage, AdSize.mrec],
                    }}
                    placeholderPadding="py-3 lg:pb-0 xl:pb-3"
                    position={1}
                    publiftName="side-1"
                    sizes={[]}
                    slotId="story-side-1"
                  />
                </div>
                {stickySpacer}
                {stickySpacer}
              </div>
            )}
            {showUpcomingMatchesWidget && (
              <UpcomingMatches
                sportPages={[pageName.toLowerCase() as SportPage]}
              />
            )}
            {isAgsTheme && pageName === 'Property' && (
              <>
                <div className="empty:hidden" id="farmBuyWidgetContainer" />
                <FarmBuyScript />
              </>
            )}
            {isRecommendationContentEnabled && (
              <div>
                <div className="sticky block sm:top-20">
                  <div className="my-6">
                    <div
                      className="mb-4 break-words border-t-2 border-gray-900 pt-2 font-inter text-base font-semibold text-gray-900 empty:hidden"
                      id="recommendationTopRightHeading"
                    />
                    <div
                      className="empty:hidden"
                      id="recommendationTopRight"
                    />
                  </div>
                </div>
                {stickySpacer}
                {stickySpacer}
              </div>
            )}
            {story.localNewsStoryList &&
              (!communityRecirculationEnabled || !isLocalContent) && (
                <div>
                  <div className="lg:top-20">
                    <InViewEvent
                      onEnterEvent={() => onView('More from Local News')}
                    >
                      <TitledStoryListTwoColumns
                        className="my-6"
                        headlineProps={{
                          onClick: (clickedStory: typeof story) => {
                            setGtmDataLayer({
                              data: {
                                action: 'click',
                                label: clickedStory.title,
                                section: 'More from Local News',
                              },
                              event: 'recirculation_click',
                            });
                          },
                          showByline: false,
                          showSignpost: false,
                        }}
                        isHeading
                        limit={limit}
                        stories={story.localNewsStoryList.stories}
                        storyListId={story.localNewsStoryList.storyListId}
                        title="More from Local News"
                      />
                    </InViewEvent>
                  </div>
                  {stickySpacer}
                  {stickySpacer}
                </div>
              )}
            {ownLocal.enabled && showComponentPlaceholder && (
              <div className="text-center">
                <OwnLocal />
              </div>
            )}
            {businessProfilesFeature.enabled &&
              businessProfilesFeature.data.widgetPosition ===
                WidgetPosition.Side &&
              (hasDirectory ? (
                <OwnLocalAds limit={1} />
              ) : (
                <div className="my-4 border-gray-900 empty:hidden lg:my-0">
                  <LocalAdUnitSidebar />
                </div>
              ))}
            {!supportNewslettersLandingPage && (
              <MailingList id="email-story" vertical />
            )}
            {showComponentPlaceholder && (
              <div className="hidden grow lg:block">
                <div className="sticky top-20">
                  <Ad
                    className="mx-auto min-w-mrec lg:max-w-mrec xl:mx-2 xl:max-w-none"
                    labelSpace="space-y-2"
                    lgSizes={{
                      defaultPlaceholderSize: AdSize.mrec,
                      sizes: [AdSize.halfPage, AdSize.mrec],
                    }}
                    placeholderPadding="py-3 lg:pb-0 xl:pb-3"
                    position={2}
                    publiftName="side-2"
                    sizes={[]}
                    slotId="story-side-2"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </Container>
      {oovvuuFeature.enabled && showComponent && (
        <Script async src="https://videos.oovvuu.com/rlps/v1/ovu_rec.js" />
      )}
      <Skimlinks />
    </TemplateWrapper>
  );
}

export default Story;
