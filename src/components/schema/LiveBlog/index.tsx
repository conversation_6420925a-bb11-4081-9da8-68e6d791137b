'use client';

import { useAppSelector } from 'store/hooks';
import { useStructuredMarkup } from 'themes/autumn/components/storyElements/services/NorkonLiveCenter/hooks';

interface LiveBlogSchemaProps {
  description: string;
  serviceId: string;
}

export default function LiveBlogSchema({
  description,
  serviceId,
}: LiveBlogSchemaProps): React.ReactElement {
  const norkonFeature = useAppSelector(
    (state) => state.features.norkonLiveblog,
  );

  const structuredData = useStructuredMarkup(
    description,
    norkonFeature,
    serviceId,
  );

  return (
    <script
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
      type="application/ld+json"
    />
  );
}
