import { ZoneItemType } from 'types/ZoneItems';
import { keysToCamel } from 'util/string';

import { pageUrlPath } from '../page';

import type { FarmbuyProperty } from 'types/Farmbuy';
import type { ContactUsFormData } from 'types/Page';
import type { PaymentStatus } from 'types/Payments';
import type {
  CricketGroup,
  InningsSummary,
  LadderPositionsProps,
  MatchDetailsCompProps,
  MatchDetailsProps,
  MatchExtraProps,
  MatchInfoProps,
  MatchStatus,
  MatchWidgetProps,
  PeriodScoresProps,
  ScoreFlow,
  TeamsType,
  TotalScoreType,
} from 'types/SportsHub';
import type { Story, StoryImage, StoryImageCropConfig } from 'types/Story';
import type {
  CollectionAuthor,
  CollectionPage,
  CollectionStoryList,
  VendorType,
  ZoneItemBase,
} from 'types/ZoneItems';
import type { UGC } from 'types/ugc';

interface UpdateStoryHeadlineOptions {
  headline: string;
  siteId: number;
  storyId: string;
  token: string;
  view?: string;
}

export enum Command {
  REORDER_ZONE_ITEM = 'ReorderZoneItem',
  MOVE_STORY_PIN = 'MoveStoryPin',
  REMOVE_STORY_PIN = 'RemoveStoryPin',
  INSERT_STORY_PIN = 'InsertStoryPin',
  CREATE_ZONE_ITEM = 'CreateZoneItem',
  DELETE_ZONE_ITEM = 'DeleteZoneItem',
  UPDATE_ZONE_ITEM = 'UpdateZoneItem',
}

export async function executeDraftingCommand<T>(
  command: Command,
  data: unknown,
): Promise<T> {
  const store = window.getStore();
  const res = await fetch(
    `${store.getState().racetracks.suzukaUrl}drafting/`,
    {
      body: JSON.stringify({
        command,
        data,
      }),
      method: 'POST',
    },
  );

  if (!res.ok) {
    throw new Error(
      `HTTP error! status: ${res.status} - message: ${res.statusText}`,
    );
  }

  return keysToCamel(await res.json()) as T;
}

export async function updateStoryHeadline({
  headline,
  siteId,
  storyId,
  token,
  view = 'homepage',
}: UpdateStoryHeadlineOptions): Promise<void> {
  const store = window.getStore();
  await fetch(
    `${
      store.getState().racetracks.suzukaUrl
    }api/v2/storydata/story/${storyId}/site/${siteId}/`,
    {
      body: JSON.stringify({
        [`${view}_title`]: headline,
      }),
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      method: 'PATCH',
    },
  );
}

interface FetchStoriesOptions {
  editMode?: boolean;
  siteId: number;
  storyListId: number;
  token: string;
  useCanonicalUrl?: boolean;
}

export interface FetchStoriesResponse {
  pinnedStoryIds: string[];
  stories: Story[];
  storyListId: number;
}

export async function fetchStories({
  editMode = false,
  siteId,
  storyListId,
  token,
  useCanonicalUrl = false,
}: FetchStoriesOptions): Promise<FetchStoriesResponse> {
  const params = new URLSearchParams({
    site_id: siteId.toString(),
    use_cache: 'false',
    use_canonical_url: String(useCanonicalUrl),
    use_publish_from_utc: '1',
    view_type: 'homepage',
    with_story_list_info: '1',
  });

  if (editMode) {
    params.append('edit_mode', 'true');
  }

  const store = window.getStore();
  const res = await fetch(
    `${
      store.getState().racetracks.suzukaUrl
    }stories/${storyListId}?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      method: 'GET',
    },
  );

  return keysToCamel(await res.json()) as FetchStoriesResponse;
}

interface UpdateStoryImageCropOptions {
  cropConfig?: StoryImageCropConfig;
  description?: string;
  image: StoryImage;
  siteId: number;
  storyId: string;
  token: string;
  view?: string;
}

export async function updateStoryImage({
  cropConfig,
  description,
  image,
  siteId,
  storyId,
  token,
  view = 'homepage',
}: UpdateStoryImageCropOptions): Promise<void> {
  const store = window.getStore();
  await fetch(
    `${
      store.getState().racetracks.suzukaUrl
    }api/v2/storydata/story/${storyId}/site/${siteId}/`,
    {
      body: JSON.stringify({
        [`${view}_lead_image`]: {
          cropConfig: cropConfig ?? image.cropConfig,
          description: description ?? image.description,
          height: image.height,
          title: image.title ?? '',
          uri: image.uri,
          width: image.width,
        },
      }),
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      method: 'PATCH',
    },
  );
}

interface ClearStoryImageCropOptions {
  siteId: number;
  storyId: string;
  token: string;
  view?: string;
}

export async function clearStoryImage({
  siteId,
  storyId,
  token,
  view = 'homepage',
}: ClearStoryImageCropOptions): Promise<void> {
  const store = window.getStore();
  await fetch(
    `${
      store.getState().racetracks.suzukaUrl
    }api/v2/storydata/story/${storyId}/site/${siteId}/`,
    {
      body: JSON.stringify({
        [`${view}_lead_image`]: null,
      }),
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      method: 'PATCH',
    },
  );
}

type PagesResponse = {
  id: number;
  link: string;
  links: {
    label: string;
    link: string;
  }[];
  name: string;
}[];

export async function fetchPages(
  siteId: number,
  token: string,
): Promise<PagesResponse> {
  const store = window.getStore();
  const url = new URL(`${store.getState().racetracks.suzukaUrl}api/v2/pages/`);

  url.search = new URLSearchParams({
    site_id: siteId.toString(),
  }).toString();

  const response = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return (await response.json()) as PagesResponse;
}

type PageCollectionsResponse = {
  id: number;
  includeDescendants: boolean;
  pages: CollectionPage[];
  title: string;
}[];

export async function fetchPageCollections(
  siteId: number,
  token: string,
): Promise<PageCollectionsResponse> {
  const store = window.getStore();
  const url = new URL(
    `${store.getState().racetracks.suzukaUrl}api/v2/page-collection/`,
  );

  url.search = new URLSearchParams({
    site_id: siteId.toString(),
  }).toString();

  const response = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return (await response.json()) as PageCollectionsResponse;
}

interface AuthorCollection {
  authors: CollectionAuthor[];
  id: number;
  title: string;
}

type AuthorCollectionsResponse = AuthorCollection[];

export async function fetchAuthorCollections(
  siteId: number,
  token: string,
): Promise<AuthorCollectionsResponse> {
  const store = window.getStore();
  const url = new URL(
    `${store.getState().racetracks.suzukaUrl}api/v2/author-collection/`,
  );

  url.search = new URLSearchParams({
    site_id: siteId.toString(),
  }).toString();

  const response = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return (await response.json()) as AuthorCollectionsResponse;
}

type StorylistCollectionsResponse = {
  id: number;
  storylists: CollectionStoryList[];
  title: string;
}[];

export async function fetchStorylistCollections(
  siteId: number,
  token: string,
): Promise<StorylistCollectionsResponse> {
  const store = window.getStore();
  const url = new URL(
    `${store.getState().racetracks.suzukaUrl}api/v2/storylist-collection/`,
  );

  url.search = new URLSearchParams({
    site_id: siteId.toString(),
  }).toString();

  const response = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return (await response.json()) as StorylistCollectionsResponse;
}

export interface StoryListResponse {
  id: number;
  title: string;
}

interface StoryListsResponse {
  count: number;
  next: null | string;
  previous: null | string;
  results: StoryListResponse[];
}

export async function fetchStoryLists([siteId, token]: [
  number,
  string,
]): Promise<StoryListResponse[]> {
  const store = window.getStore();
  const url = new URL(
    `${store.getState().racetracks.suzukaUrl}api/v2/storylist/`,
  );

  url.search = new URLSearchParams({
    fields: 'id,title',
    sites: siteId.toString(),
  }).toString();

  const response = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const { results } = (await response.json()) as StoryListsResponse;

  return results;
}

export async function fetchStoryListsByTitle(
  title: string,
  siteId: number,
  signal?: AbortSignal,
): Promise<StoryListResponse[]> {
  const store = window.getStore();
  const url = new URL(
    `${store.getState().racetracks.suzukaUrl}api/v2/storylist/`,
  );

  url.search = new URLSearchParams({
    fields: 'id,title',
    sites: siteId.toString(),
    title,
  }).toString();

  const response = await fetch(url.toString(), { signal });

  const { results } = (await response.json()) as StoryListsResponse;

  return results;
}

function objectKeyForValue(
  obj: Record<string, unknown>,
  value: unknown,
): null | string {
  const keys = Object.keys(obj).filter((x) => obj[x] === value);
  return keys.length > 0 ? keys[0] : null;
}

interface CreateZoneItemResponse {
  elementId: number;
  zoneItemId: number;
}

export async function createZoneItem(
  type: ZoneItemType,
  zone: string,
  pageId: number,
  siteId: number,
  data: ZoneItemBase,
): Promise<CreateZoneItemResponse> {
  const typeKey = objectKeyForValue(ZoneItemType, type);

  if (typeKey === null) {
    throw new Error(`Unknown zone item type: ${type.toString()}`);
  }

  return executeDraftingCommand<CreateZoneItemResponse>(
    Command.CREATE_ZONE_ITEM,
    {
      element_fields: {
        [typeKey]: data,
      },
      page_id: pageId,
      site_id: siteId,
      zone_item_fields: {
        zone,
      },
      zone_item_type: typeKey,
    },
  );
}

type UpdateZoneItemResponse = Record<string, never>;

export async function updateZoneItem<T extends ZoneItemBase>(
  id: number,
  type: ZoneItemType,
  data: T,
): Promise<UpdateZoneItemResponse> {
  const typeKey = objectKeyForValue(ZoneItemType, type);

  if (typeKey === null) {
    throw new Error(`Unknown zone item type: ${type.toString()}`);
  }

  return executeDraftingCommand<UpdateZoneItemResponse>(
    Command.UPDATE_ZONE_ITEM,
    {
      element_id: id,
      update_fields: {
        [typeKey]: data,
      },
      zone_item_type: typeKey,
    },
  );
}

type DeleteZoneItemResponse = Record<string, never>;

export async function deleteZoneItem(
  id: number,
  type: ZoneItemType,
): Promise<DeleteZoneItemResponse> {
  const typeKey = objectKeyForValue(ZoneItemType, type);

  if (typeKey === null) {
    throw new Error(`Unknown zone item type: ${type.toString()}`);
  }

  return executeDraftingCommand<DeleteZoneItemResponse>(
    Command.DELETE_ZONE_ITEM,
    {
      zone_item_id: id,
      zone_item_type: typeKey,
    },
  );
}

export interface FetchStoriesForStoryListProps {
  extraTags?: string;
  limit?: number;
  offset?: number;
  showCanonicalSite?: boolean;
  showRegion?: boolean;
  siteId: number;
  storyListId: number;
  useCanonicalUrl: boolean;
}

export interface FetchUgcItemsForUgcListProps {
  category?: string;
  dateRange?: string;
  distance?: string;
  limit?: number;
  location?: string;
  offset?: number;
  pinnedUgcOnly?: boolean;
  siteId: number;
  ugcListId: number;
}

export async function fetchStoriesForStoryList({
  extraTags,
  limit,
  offset,
  showCanonicalSite = false,
  signal,
  siteId,
  storyListId,
  useCanonicalUrl,
}: FetchStoriesForStoryListProps & { signal?: AbortSignal }): Promise<
  Story[]
> {
  const store = window.getStore();
  const url = new URL(
    `${store.getState().racetracks.suzukaUrl}stories/${storyListId}`,
  );

  url.search = new URLSearchParams({
    extra_tags: extraTags ?? '',
    limit: limit?.toString() ?? '',
    offset: offset?.toString() ?? '',
    show_canonical_site: showCanonicalSite.toString(),
    site_id: siteId.toString(),
    use_canonical_url: useCanonicalUrl.toString(),
    use_publish_from_utc: 'true',
  }).toString();

  const response = await fetch(url.toString(), { signal });

  return keysToCamel(await response.json()) as Story[];
}

export async function fetchUgcItemsForUgcList({
  category,
  dateRange,
  distance,
  limit,
  location,
  offset,
  pinnedUgcOnly,
  siteId,
  ugcListId,
}: FetchUgcItemsForUgcListProps): Promise<UGC[]> {
  const params = new URLSearchParams({
    ...(category !== undefined && { category }),
    ...(dateRange !== undefined && { date_range: dateRange }),
    ...(distance !== undefined && { distance_km: distance }),
    limit: limit?.toString() ?? '',
    ...(location !== undefined && { location }),
    offset: offset?.toString() ?? '',
    show_all_sites: location ? 'true' : 'false',
    site_id: siteId.toString(),
    ...(pinnedUgcOnly !== undefined && {
      pinned_ugc_only: pinnedUgcOnly.toString(),
    }),
  }).toString();

  const response = await fetch(`/ugc/${ugcListId}?${params}`);

  return keysToCamel(await response.json()) as UGC[];
}

export interface PromotionResponse {
  accountType: number;
  adContactMe: boolean;
  adCopy: string;
  adImage: string;
  adTitle: string;
  businessUrl: string;
  categoryName: string;
  categorySlug: string;
  id: number;
  name: string;
  profileUrl: string;
  slug: string;
  telephone: string;
}

export interface FetchPromotionsResponse {
  items: PromotionResponse[];
}

interface FetchPromotionsProps {
  limit: number;
}

export async function fetchPromotions({
  limit,
}: FetchPromotionsProps): Promise<PromotionResponse[]> {
  const params = new URLSearchParams({
    limit: String(limit),
  });

  const res = await fetch(
    `/promotions/website_content_esov/?${params.toString()}`,
    {
      headers: {
        Accept: 'application/json',
      },
      method: 'GET',
    },
  );

  const { items } = (await res.json()) as FetchPromotionsResponse;
  return keysToCamel(items) as PromotionResponse[];
}

export async function fetchBusinessFeatureAds(
  tag: string,
): Promise<PromotionResponse[]> {
  const params = new URLSearchParams({
    tag,
  });

  const res = await fetch(
    `/promotions/local/business-feature-ads/?${params.toString()}`,
    {
      headers: {
        Accept: 'application/json',
      },
      method: 'GET',
    },
  );

  const { items } = (await res.json()) as FetchPromotionsResponse;
  return keysToCamel(items) as PromotionResponse[];
}

export interface FetchBFStoryListResponse {
  stories: Story[];
}

export interface FireEvent {
  category: string;
  distance: string;
  id: string;
  minuteAgo: number;
  title: string;
}

export interface FetchRfsResponse {
  events: FireEvent[];
}

export interface TrafficEvent {
  category: string;
  distance: number;
  headline: string;
  id: string;
  minuteAgo: number;
  suburb: string;
  title: string;
}

export interface FetchTrafficResponse {
  features: TrafficEvent[];
}

interface FetchTrafficProps {
  limit: number;
  range: number;
}

interface FetchBFStoryListProps {
  limit: number;
  siteId: number;
}

interface FetchRfsProps {
  limit: number;
  range: number;
}

export async function fetchBFStoryList({
  limit,
  siteId,
}: FetchBFStoryListProps): Promise<Story[]> {
  const params = new URLSearchParams({
    // eslint-disable-next-line rulesdir/prefer-use-date
    _: String(Math.floor(Date.now())),
    limit: String(limit),
    site_id: String(siteId),
  });

  const res = await fetch(`/stories/bf_story_list/?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });

  const { stories } = (await res.json()) as FetchBFStoryListResponse;

  return keysToCamel(stories) as Story[];
}

export async function fetchRfsList({
  limit,
  range,
}: FetchRfsProps): Promise<FireEvent[]> {
  const params = new URLSearchParams({
    // eslint-disable-next-line rulesdir/prefer-use-date
    _: String(Math.floor(Date.now())),
    limit: String(limit),
    range: String(range),
  });

  const res = await fetch(`/rfs-strap/?${params.toString()}`);

  const events = (await res.json()) as FetchRfsResponse;

  return keysToCamel(events) as FireEvent[];
}

export async function fetchTrafficList({
  limit,
  range,
}: FetchTrafficProps): Promise<TrafficEvent[]> {
  const params = new URLSearchParams({
    // eslint-disable-next-line rulesdir/prefer-use-date
    _: String(Math.floor(Date.now())),
    limit: String(limit),
    range: String(range),
  });

  const res = await fetch(`/notice-board/traffic/?${params.toString()}`);

  const events = (await res.json()) as FetchTrafficResponse;

  return keysToCamel(events.features) as TrafficEvent[];
}

type RevokeBetaAccessReponse =
  | {
      success: true;
    }
  | {
      message: string;
      success: false;
    };

export async function revokeBetaAccess(
  uid: string,
): Promise<RevokeBetaAccessReponse> {
  const params = new URLSearchParams({
    uid,
  });

  const res = await fetch(`/api/beta-access/revoke/?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });

  return (await res.json()) as RevokeBetaAccessReponse;
}

export async function fetchExternalStrap(
  vendor: VendorType,
): Promise<Record<string, Story[]>> {
  const res = await fetch(pageUrlPath(`external-strap/${vendor}`), {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });

  return keysToCamel(await res.json()) as Record<string, Story[]>;
}

interface FetchSportsResultsStoryListProps {
  limit: number;
  offset: number;
}

export async function fetchSportsResultsStoryList({
  limit,
  offset,
}: FetchSportsResultsStoryListProps): Promise<Story[]> {
  const params = new URLSearchParams({
    limit: String(limit),
    offset: String(offset),
  });

  const res = await fetch(`?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    method: 'GET',
  });

  const { stories } = (await res.json()) as FetchBFStoryListResponse;

  return stories;
}

interface FetchDailymotionStoryListProps {
  limit: number;
  page: number;
  pageUrl: string;
  siteId: number;
  urlParams: string;
}

export async function fetchDailymotionStoryList({
  limit,
  page,
  pageUrl,
  siteId,
  urlParams,
}: FetchDailymotionStoryListProps): Promise<Story[]> {
  const params = new URLSearchParams({
    limit: String(limit),
    page: String(page),
    page_url: pageUrl,
    site_id: String(siteId),
    url_params: urlParams,
  });

  const res = await fetch(`/video/?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    method: 'GET',
  });

  const { stories } = (await res.json()) as FetchBFStoryListResponse;

  return stories;
}

export interface ValidatePaymentsResponseDataObject {
  html: string;
  id?: string;
  name: PaymentStatus;
}

export interface FetchValidatePaymentsResponse {
  data: ValidatePaymentsResponseDataObject | PaymentStatus;
  message: string;
  success: boolean;
}

interface FetchValidatePaymentsProps {
  token: string;
}

export async function fetchValidatePayments({
  token,
}: FetchValidatePaymentsProps): Promise<FetchValidatePaymentsResponse> {
  const params = new URLSearchParams({
    token: String(token),
  });

  const res = await fetch(`/api/validate-payments/?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });

  return (await res.json()) as FetchValidatePaymentsResponse;
}

export interface ValidatePremiumSubscriptionResponseDataObject {
  extended: boolean;
  reload: boolean;
  token: string | null;
}

export interface FetchValidatePremiumSubscriptionResponse {
  data: ValidatePremiumSubscriptionResponseDataObject;
  message: string;
  success: boolean;
}

interface FetchValidatePremiumSubscriptionProps {
  premiumToken?: string;
  token: string;
}

export async function fetchValidatePremiumSubscription({
  premiumToken,
  token,
}: // eslint-disable-next-line @stylistic/max-len
FetchValidatePremiumSubscriptionProps): Promise<FetchValidatePremiumSubscriptionResponse> {
  const params = new URLSearchParams(
    premiumToken ? { premiumToken, token } : { token },
  );

  const res = await fetch(
    `/api/validate-premium-subscriptions/?${params.toString()}`,
    {
      headers: {
        Accept: 'application/json',
      },
      method: 'GET',
    },
  );

  return (await res.json()) as FetchValidatePremiumSubscriptionResponse;
}

interface FetchAuthorStoryListProps {
  page: number;
}

export async function fetchAuthorStoryList({
  page,
}: FetchAuthorStoryListProps): Promise<Story[]> {
  const params = new URLSearchParams({
    page: String(page),
  });

  const res = await fetch(`?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    method: 'GET',
  });

  const { stories } = (await res.json()) as FetchBFStoryListResponse;

  return keysToCamel(stories) as Story[];
}

interface SubmitAdvertiseWithUsResponse {
  success: number;
}

export async function submitAdvertiseWithUsForm(
  csrftoken: string,
  data: ContactUsFormData,
): Promise<SubmitAdvertiseWithUsResponse> {
  const dataSnakeFormat = {
    business_name: data.businessName,
    comments: data.comments,
    contact_number: data.contactNumber,
    email: data.email,
    interested_in: data.interestedIn,
    name: data.name,
    postcode: data.postcode,
  };

  const res = await fetch('/business/advertise-us/', {
    body: JSON.stringify(dataSnakeFormat),
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'X-CSRFToken': csrftoken,
    },
    method: 'POST',
  });

  return (await res.json()) as SubmitAdvertiseWithUsResponse;
}

export interface PublisherProvidedIdResponse {
  ppid: string;
}

export async function fetchAdPublisherProvidedId(
  uid: string,
): Promise<PublisherProvidedIdResponse> {
  const params = new URLSearchParams({
    uid,
  });
  const res = await fetch(`/api/publisher-provided-id/?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });
  return (await res.json()) as PublisherProvidedIdResponse;
}

interface CurrentRoundSportMatchesProps {
  allFinals?: boolean;
  compLevelId: number;
  matchSeason: string;
  matchStatus?: MatchStatus;
  matchStatusIn?: MatchStatus[];
  ordering?: string;
}

export interface MatchResponse {
  data: MatchWidgetProps;
  id: number;
  ladderPositions: LadderPositionsProps;
  match?: MatchDetailsProps;
}

export type FinalsMatches = Record<string, MatchResponse>;

export interface CurrentRoundSportMatchesResponse {
  results: {
    currentRound: number;
    matches: MatchResponse[];
  };
}

export async function fetchCurrentRoundSportMatches({
  allFinals = false,
  compLevelId,
  matchSeason,
  matchStatus,
  matchStatusIn,
}: CurrentRoundSportMatchesProps): Promise<CurrentRoundSportMatchesResponse> {
  const urlParams: UrlParamsProps = {
    match__comp__comp_level_id: compLevelId,
    match__comp__season: matchSeason,
  };
  if (allFinals) {
    urlParams.all_finals = '';
  }
  if (matchStatus) {
    urlParams.match__match_status = matchStatus;
  }
  if (matchStatusIn?.length) {
    urlParams.match__match_status__in = matchStatusIn;
  }
  const params = new URLSearchParams(urlParams as URLSearchParams);
  const res = await fetch(
    `/sports-hub-api/match-fixture/current-round/?${params.toString()}`,
    {
      headers: {
        Accept: 'application/json',
      },
      method: 'GET',
    },
  );

  return keysToCamel(await res.json()) as CurrentRoundSportMatchesResponse;
}

export interface SportMatchesProps {
  allFinals?: boolean;
  compLevelId?: number;
  compLevelIdIn?: number[];
  matchSeason?: string;
  matchStatus?: MatchStatus;
  matchStatusIn?: MatchStatus[];
  ordering?: string;
  roundNumber?: number;
}

export interface SportMatchesResponse {
  results: MatchResponse[];
}

interface UrlParamsProps {
  all_finals?: string;
  match__comp__comp_level_id?: number;
  match__comp__comp_level_id__in?: number[];
  match__comp__season?: string;
  match__match_status?: MatchStatus;
  match__match_status__in?: MatchStatus[];
  match__round_number?: number;
  match__round_number__gte?: number;
  ordering?: string;
}

export async function fetchSportMatches({
  allFinals = false,
  compLevelId,
  compLevelIdIn,
  matchSeason,
  matchStatus,
  matchStatusIn,
  roundNumber = -1,
}: SportMatchesProps): Promise<SportMatchesResponse> {
  const urlParams: UrlParamsProps = {};
  if (roundNumber !== -1) {
    if (allFinals) {
      urlParams.match__round_number__gte = roundNumber;
    } else {
      urlParams.match__round_number = roundNumber;
    }
  }
  if (compLevelId && matchSeason) {
    urlParams.match__comp__comp_level_id = compLevelId;
    urlParams.match__comp__season = matchSeason;
  }
  if (matchStatus) {
    urlParams.match__match_status = matchStatus;
  }
  if (matchStatusIn?.length) {
    urlParams.match__match_status__in = matchStatusIn;
  }
  if (compLevelIdIn?.length && matchSeason) {
    urlParams.match__comp__comp_level_id__in = compLevelIdIn;
  }
  const params = new URLSearchParams(urlParams as URLSearchParams);
  const res = await fetch(
    `/sports-hub-api/match-fixture/?${params.toString()}`,
    {
      headers: {
        Accept: 'application/json',
      },
      method: 'GET',
    },
  );

  return keysToCamel(await res.json()) as SportMatchesResponse;
}

interface SportLadderProps {
  compLevelId: number;
  matchSeason: string;
}

export interface SportLadderResultData {
  team: TeamsType;
}

interface SportLadderResult {
  data: SportLadderResultData | CricketGroup[];
}

export interface SportLadderResponse {
  results: SportLadderResult[];
}

export async function fetchSportLadder({
  compLevelId,
  matchSeason,
}: SportLadderProps): Promise<SportLadderResponse> {
  const params = new URLSearchParams({
    comp__comp_level_id: compLevelId.toString(),
    comp__season: matchSeason,
  });
  const res = await fetch(`/sports-hub-api/ladder/?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });

  return keysToCamel(await res.json()) as SportLadderResponse;
}

interface SportMatchDetail {
  matchId: number;
}

export interface SportMatchDetailResponse {
  comp: MatchDetailsCompProps;
  extra?: MatchExtraProps;
  fixture: {
    data: MatchWidgetProps;
  };
  ladderPositions: LadderPositionsProps;
  stats?: {
    data: {
      inningsSummary?: InningsSummary;
      matchInfo?: MatchInfoProps;
      matchScores?: {
        periodScores?: PeriodScoresProps[];
        totalScores?: TotalScoreType[];
      };
      scoreFlow?: {
        score: ScoreFlow[];
      };
    };
  };
}

export async function fetchSportMatchDetail({
  matchId,
}: SportMatchDetail): Promise<SportMatchDetailResponse> {
  const res = await fetch(`/sports-hub-api/match/${matchId}/`, {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });

  return keysToCamel(await res.json()) as SportMatchDetailResponse;
}

interface FarmbuyFeaturedPropertiesResponse {
  properties: FarmbuyProperty[];
}

// eslint-disable-next-line @stylistic/max-len
export async function fetchFarmbuyFeaturedProperties(): Promise<FarmbuyFeaturedPropertiesResponse> {
  const res = await fetch('/api/farmbuy/featured-properties/', {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });

  if (!res.ok) {
    throw new Error(
      `Failed to fetch Farmbuy featured properties. Reason: ${res.statusText}`,
    );
  }

  return keysToCamel(await res.json()) as FarmbuyFeaturedPropertiesResponse;
}
